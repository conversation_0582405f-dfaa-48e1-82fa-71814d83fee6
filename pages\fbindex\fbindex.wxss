/**index.wxss**/

/* 返回按钮样式 */
.back-button {
  background-color: #1296DB;
  color: white;
  padding: 20rpx 30rpx;
  margin: 20rpx 30rpx;
  border-radius: 8rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(18, 150, 219, 0.3);
}

.back-text {
  font-size: 28rpx;
  font-weight: bold;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  flex: 1;
}

.welcome {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.company-name,
.phone,
.no-company {
  font-size: 32rpx !important;
  color: rgb(114, 108, 108) !important;
}

.switch-company {
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
}

.switch-text {
  font-size: 24rpx;
  color: #666;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1296DB;
  display: block;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}




.user-card {
  background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
}



.detail-number {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}



/* 快速操作 */
.quick-actions {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.action-item {
  background-color: white;
  padding: 40rpx 20rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 26rpx;
  color: #333;
}

/* 工程列表 */
.recent-projects {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-toggle {
  font-size: 24rpx;
  color: #1296DB;
  padding: 8rpx 16rpx;
  background-color: #f0f8ff;
  border-radius: 16rpx;
}

.view-all {
  font-size: 24rpx;
  color: #1296DB;
  padding: 8rpx 16rpx;
  background-color: #f0f8ff;
  border-radius: 16rpx;
}

/* 搜索栏样式 */
.search-bar {
  background-color: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}



.project-list {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 120rpx;
}

.project-item:last-child {
  border-bottom: none;
}

.project-info {
  flex: 1;
}

.project-main {
  margin-bottom: 12rpx;
}

.project-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.project-code,.project-unit {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 2rpx;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

.task-count {
  font-size: 24rpx;
  color: #999;
}

.created-time {
  font-size: 22rpx;
  color: #ccc;
}



/* 公司选择弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.company-list {
  margin-bottom: 30rpx;
}

.company-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.company-item:last-child {
  border-bottom: none;
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.company-check {
  font-size: 32rpx;
  color: #1296DB;
  font-weight: bold;
}

.company-actions {
  display: flex;
  gap: 20rpx;
}

.company-actions .btn {
  flex: 1;
  margin: 0;
}

/* 空状态和加载状态 */
.empty-state,
.no-company-tip {
  background-color: white;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.empty-icon,
.tip-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text,
.tip-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.empty-desc,
.tip-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  display: block;
  margin-bottom: 30rpx;
}

.loading-state {
  background-color: white;
  border-radius: 12rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.no-company-tip .btn {
  margin-top: 20rpx;
}

/* 视频时长控制弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 16rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.modal-body {
  padding: 30rpx;
}

.settings-description {
  background-color: #f0f8ff;
  border-left: 4rpx solid #1296DB;
  padding: 20rpx;
  margin-bottom: 30rpx;
  border-radius: 8rpx;
}

.desc-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}



.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.modal-footer .btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

.btn-primary {
  background-color: #1296DB;
  color: white;
}

.btn-primary[disabled] {
  background-color: #ccc;
  color: #999;
}

