# 登录状态调试指南

## 问题现象
重新编译后有登录状态但还是会自动跳转到Login页面

## 修复内容总结

### 1. 主要修复点
- **app.js**: 添加了完整的全局登录状态检查逻辑
- **pages/index/index.js**: 优化了页面级登录检查，优先使用全局状态
- **修复变量引用错误**: 将 `userInfo` 变量改为 `'userInfo'` 字符串

### 2. 新的登录流程
```
小程序启动 (app.onLaunch)
    ↓
检查微信session → 检查本地userInfo → 设置全局状态
    ↓
页面加载 (index.onLoad)
    ↓
优先检查全局状态 → 如果有效直接使用 → 如果无效执行本地检查
```

## 调试步骤

### 步骤1: 清理环境测试
```javascript
// 在开发者工具控制台执行
wx.clearStorageSync()
console.log('缓存已清理')
```

### 步骤2: 首次登录测试
1. 重新编译小程序
2. 观察控制台输出，应该看到：
   ```
   === App启动 - 本地用户信息检查 ===
   ❌ 无有效登录信息
   ```
3. 完成登录流程
4. 登录成功后应该看到：
   ```
   登录成功，已更新全局状态
   ```

### 步骤3: 重新编译测试（关键测试）
1. 登录成功后，在开发者工具中重新编译
2. 观察控制台输出，应该看到：
   ```
   === App启动 - 本地用户信息检查 ===
   ✅ 发现有效的本地用户信息，设置全局登录状态为已登录
   === index页面加载 - 开始检查登录状态 ===
   ✅ 使用全局登录状态，直接设置用户信息
   ```
3. **关键验证点**: 页面应该直接显示首页内容，不应该跳转到Login页面

### 步骤4: 验证存储状态
```javascript
// 在控制台检查存储状态
const userInfo = wx.getStorageSync('userInfo')
const openId = wx.getStorageSync('OpenId')
console.log('userInfo:', userInfo)
console.log('openId:', openId)

// 检查全局状态
const app = getApp()
console.log('全局登录状态:', app.globalData.isLoggedIn)
console.log('全局用户信息:', app.globalData.userInfo)
```

## 可能的问题排查

### 问题1: 仍然跳转到Login页面
**排查方法:**
1. 检查控制台是否显示 "有OpenId但无用户信息，跳转到登录页"
2. 如果是，说明 userInfo 存储有问题

**解决方案:**
```javascript
// 检查userInfo的完整性
const userInfo = wx.getStorageSync('userInfo')
console.log('userInfo完整性检查:', {
    exists: !!userInfo,
    hasPersonId: !!(userInfo && userInfo.PersonId),
    hasUserId: !!(userInfo && userInfo.userId),
    userInfo: userInfo
})
```

### 问题2: 全局状态未正确设置
**排查方法:**
1. 检查 app.onLaunch 是否正常执行
2. 检查是否有错误阻止了状态设置

**解决方案:**
```javascript
// 手动设置全局状态进行测试
const app = getApp()
const userInfo = wx.getStorageSync('userInfo')
if (userInfo && (userInfo.PersonId || userInfo.userId)) {
    app.globalData.isLoggedIn = true
    app.globalData.userInfo = userInfo
    console.log('手动设置全局状态成功')
}
```

### 问题3: 微信session检查失败
**排查方法:**
```javascript
// 检查微信session状态
wx.checkSession({
    success: () => console.log('微信session有效'),
    fail: () => console.log('微信session无效')
})
```

## 预期结果

修复后的正常流程应该是：
1. **首次启动**: 引导用户登录
2. **重新编译**: 自动恢复登录状态，直接显示首页
3. **Session过期**: 自动清理数据，引导重新登录

## 注意事项

1. 确保在登录成功、退出登录等关键节点都要同步更新全局状态
2. 调试时注意观察控制台的详细日志输出
3. 如果问题持续，可以尝试完全清理缓存后重新测试
